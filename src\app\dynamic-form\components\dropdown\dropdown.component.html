<!-- Unified Dropdown Component Template -->
<div class="dropdown-input-container" [class.disabled]="isDisabled || isReadonly">
  <!-- Input field with search capability -->
  <input 
    [id]="uniqueId"
    [formControl]="formControl"
    [class]="inputClass"
    [placeholder]="placeholderText"
    [disabled]="isDisabled"
    [readonly]="isReadonly"
    type="text"
    autocomplete="off"
    (input)="onInputChange($event)"
    (focus)="onInputFocus()"
    (blur)="onInputBlur()" />
  
  <!-- Arrow button to toggle dropdown -->
  @if (showArrowButton) {
    <button 
      type="button" 
      class="dropdown-arrow-btn" 
      (click)="toggleDropdown()" 
      [disabled]="isDisabled || isReadonly"
      [matTooltip]="tooltipText">
      <mat-icon>{{ dropdownArrowIcon }}</mat-icon>
    </button>
  }
  
  <!-- Dropdown list for filtered results -->
  @if (showDropdown) {
    <div class="dropdown-list" [style.max-height]="dropdownMaxHeight">
      <!-- Loading state -->
      @if (isLoading) {
        <div class="dropdown-loading">
          <mat-icon>refresh</mat-icon>
          Loading...
        </div>
      }
      
      <!-- Options list -->
      @else if (filteredOptions && filteredOptions.length > 0) {
        @for (option of filteredOptions; track trackByOptionId($index, option)) {
          <div class="dropdown-item" (click)="selectOption(option)">
            <!-- ID dropdowns: show ID property -->
            @if (config.type === 'id') {
              {{ option['ID'] }}
            }
            <!-- Type and Foreign Key dropdowns: show only ROW_ID -->
            @else if (config.type === 'type' || config.type === 'foreignKey') {
              {{ option.ROW_ID }}
            }
            <!-- Regular dropdowns: show all properties -->
            @else {
              @for (key of getKeys(option); track trackByKey($index, key)) {
                {{ option[key] }}&nbsp;
              }
            }
          </div>
        }
      }
      
      <!-- Empty state -->
      @else {
        <div class="dropdown-empty">
          {{ emptyMessage }}
        </div>
      }
    </div>
  }
</div>
